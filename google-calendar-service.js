/**
 * Google Calendar Service for CalenTask Chrome Extension
 * Handles Google Calendar API interactions
 */
class GoogleCalendarService {
  constructor(authService) {
    this.authService = authService;
    this.baseUrl = 'https://www.googleapis.com/calendar/v3';
    this.calendars = [];
    this.events = [];
    this.lastSyncTime = null;
    this.syncInterval = null;
    this.isEnabled = false;

    // Load settings from storage
    this.loadSettings();
  }

  /**
   * Load service settings from Chrome storage
   */
  async loadSettings() {
    try {
      // Wait for auth service to be initialized
      if (this.authService && this.authService.waitForInitialization) {
        await this.authService.waitForInitialization();
      }

      const result = await chrome.storage.local.get([
        'google_calendar_enabled',
        'google_calendar_sync_interval',
        'google_calendar_last_sync',
        'google_calendar_events'
      ]);

      this.isEnabled = result.google_calendar_enabled || false;
      this.lastSyncTime = result.google_calendar_last_sync || null;
      this.events = result.google_calendar_events || [];

      console.log('Google Calendar settings loaded:', {
        isEnabled: this.isEnabled,
        lastSyncTime: this.lastSyncTime,
        eventCount: this.events.length
      });

      const syncInterval = result.google_calendar_sync_interval || 15; // Default 15 minutes
      this.setupAutoSync(syncInterval);
    } catch (error) {
      console.error('Error loading calendar settings:', error);
    }
  }

  /**
   * Save service settings to Chrome storage
   */
  async saveSettings() {
    try {
      await chrome.storage.local.set({
        google_calendar_enabled: this.isEnabled,
        google_calendar_last_sync: this.lastSyncTime,
        google_calendar_events: this.events
      });
    } catch (error) {
      console.error('Error saving calendar settings:', error);
    }
  }

  /**
   * Enable Google Calendar integration
   */
  async enable() {
    console.log('Attempting to enable Google Calendar integration...');

    if (!this.authService) {
      throw new Error('Authentication service not available');
    }

    // Get the most up-to-date authentication status
    const authStatus = this.authService.getAuthStatus();
    console.log('Current auth service state:', authStatus);

    // Wait for auth service initialization if needed
    if (!authStatus.isInitialized) {
      console.log('Auth service not initialized, waiting...');
      await this.authService.waitForInitialization();
    }

    // Get fresh auth status after waiting for initialization
    const freshAuthStatus = this.authService.getAuthStatus();
    console.log('Fresh auth status after initialization:', freshAuthStatus);

    if (!freshAuthStatus.isAuthenticated) {
      console.error('Authentication check failed:', {
        isAuthenticated: freshAuthStatus.isAuthenticated,
        hasAccessToken: freshAuthStatus.hasAccessToken,
        isTokenExpired: freshAuthStatus.isTokenExpired,
        isInitialized: freshAuthStatus.isInitialized
      });
      throw new Error('Must be authenticated to enable Google Calendar');
    }

    if (freshAuthStatus.isTokenExpired) {
      console.error('Token is expired:', {
        tokenExpiry: freshAuthStatus.tokenExpiry,
        currentTime: Date.now()
      });
      throw new Error('Authentication token has expired');
    }

    console.log('Authentication verified, proceeding with calendar integration...');

    this.isEnabled = true;
    await this.saveSettings();

    try {
      await this.syncCalendarEvents();
      console.log('Google Calendar sync completed successfully');
    } catch (syncError) {
      console.error('Initial calendar sync failed:', syncError);
      // Don't fail the enable operation if sync fails - user can manually sync later
    }

    this.setupAutoSync(15); // Sync every 15 minutes
    console.log('Google Calendar integration enabled successfully');
  }

  /**
   * Disable Google Calendar integration
   */
  async disable() {
    this.isEnabled = false;
    this.events = [];
    this.clearAutoSync();
    await this.saveSettings();
  }

  /**
   * Setup automatic syncing
   */
  setupAutoSync(intervalMinutes = 15) {
    this.clearAutoSync();

    if (this.isEnabled && this.authService.isAuthenticated) {
      this.syncInterval = setInterval(() => {
        this.syncCalendarEvents().catch(error => {
          console.error('Auto-sync failed:', error);
        });
      }, intervalMinutes * 60 * 1000);
    }
  }

  /**
   * Clear automatic syncing
   */
  clearAutoSync() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
  }

  /**
   * Get list of user's calendars
   */
  async getCalendarList() {
    if (!this.authService.isAuthenticated) {
      throw new Error('Not authenticated');
    }

    try {
      const response = await this.authService.makeAuthenticatedRequest(
        `${this.baseUrl}/users/me/calendarList`
      );

      if (response.ok) {
        const data = await response.json();
        this.calendars = data.items || [];
        return this.calendars;
      } else {
        throw new Error(`Failed to fetch calendars: ${response.status}`);
      }
    } catch (error) {
      console.error('Error fetching calendar list:', error);
      throw error;
    }
  }

  /**
   * Get events from user's primary calendar
   */
  async getCalendarEvents(calendarId = 'primary', timeMin = null, timeMax = null) {
    if (!this.authService.isAuthenticated) {
      throw new Error('Not authenticated');
    }

    // Default to current week if no time range specified
    if (!timeMin) {
      const now = new Date();
      const weekStart = new Date(now);
      weekStart.setDate(now.getDate() - now.getDay()); // Start of week (Sunday)
      weekStart.setHours(0, 0, 0, 0);
      timeMin = weekStart.toISOString();
    }

    if (!timeMax) {
      const now = new Date();
      const weekEnd = new Date(now);
      weekEnd.setDate(now.getDate() - now.getDay() + 6); // End of week (Saturday)
      weekEnd.setHours(23, 59, 59, 999);
      timeMax = weekEnd.toISOString();
    }

    try {
      const params = new URLSearchParams({
        timeMin: timeMin,
        timeMax: timeMax,
        singleEvents: 'true',
        orderBy: 'startTime',
        maxResults: '250'
      });

      const response = await this.authService.makeAuthenticatedRequest(
        `${this.baseUrl}/calendars/${encodeURIComponent(calendarId)}/events?${params}`
      );

      if (response.ok) {
        const data = await response.json();
        return data.items || [];
      } else {
        throw new Error(`Failed to fetch events: ${response.status}`);
      }
    } catch (error) {
      console.error('Error fetching calendar events:', error);
      throw error;
    }
  }

  /**
   * Sync calendar events from Google Calendar
   */
  async syncCalendarEvents() {
    if (!this.isEnabled || !this.authService.isAuthenticated) {
      return;
    }

    try {
      console.log('Syncing Google Calendar events...');

      // Get events for the current week and next week
      const now = new Date();
      const weekStart = new Date(now);
      weekStart.setDate(now.getDate() - now.getDay() - 7); // Start from last week
      weekStart.setHours(0, 0, 0, 0);

      const weekEnd = new Date(now);
      weekEnd.setDate(now.getDate() - now.getDay() + 14); // End at next week
      weekEnd.setHours(23, 59, 59, 999);

      const events = await this.getCalendarEvents(
        'primary',
        weekStart.toISOString(),
        weekEnd.toISOString()
      );

      // Transform Google Calendar events to our format
      this.events = events.map(event => this.transformGoogleEvent(event));

      this.lastSyncTime = new Date().toISOString();
      await this.saveSettings();

      console.log(`Synced ${this.events.length} Google Calendar events`);

      // Notify listeners about the sync
      this.notifyEventUpdate();

      return this.events;
    } catch (error) {
      console.error('Failed to sync calendar events:', error);
      throw error;
    }
  }

  /**
   * Transform Google Calendar event to our internal format
   */
  transformGoogleEvent(googleEvent) {
    const isAllDay = !googleEvent.start.dateTime;

    let startDate, endDate;

    if (isAllDay) {
      // All-day events use date instead of dateTime
      startDate = new Date(googleEvent.start.date + 'T00:00:00');
      endDate = googleEvent.end.date ? new Date(googleEvent.end.date + 'T00:00:00') : startDate;
    } else {
      startDate = new Date(googleEvent.start.dateTime);
      endDate = googleEvent.end.dateTime ? new Date(googleEvent.end.dateTime) : startDate;
    }

    return {
      id: `google_${googleEvent.id}`,
      title: googleEvent.summary || 'Untitled Event',
      description: googleEvent.description || '',
      date: startDate,
      endTime: isAllDay ? null : endDate,
      isFullDay: isAllDay,
      isGoogleEvent: true,
      googleEventId: googleEvent.id,
      location: googleEvent.location || '',
      attendees: googleEvent.attendees || [],
      htmlLink: googleEvent.htmlLink || '',
      status: googleEvent.status || 'confirmed',
      created: googleEvent.created ? new Date(googleEvent.created) : null,
      updated: googleEvent.updated ? new Date(googleEvent.updated) : null
    };
  }

  /**
   * Get all synced Google Calendar events
   */
  getEvents() {
    return this.events;
  }

  /**
   * Get events for a specific date range
   */
  getEventsInRange(startDate, endDate) {
    return this.events.filter(event => {
      const eventDate = new Date(event.date);
      return eventDate >= startDate && eventDate <= endDate;
    });
  }

  /**
   * Check if service is enabled and authenticated
   */
  isReady() {
    return this.isEnabled &&
           this.authService &&
           this.authService.isAuthenticated &&
           this.authService.isInitialized;
  }

  /**
   * Get sync status information
   */
  getSyncStatus() {
    return {
      isEnabled: this.isEnabled,
      isAuthenticated: this.authService.isAuthenticated,
      lastSyncTime: this.lastSyncTime,
      eventCount: this.events.length,
      isReady: this.isReady()
    };
  }

  /**
   * Notify listeners about event updates
   */
  notifyEventUpdate() {
    // Dispatch custom event for calendar updates
    const event = new CustomEvent('googleCalendarEventsUpdated', {
      detail: {
        events: this.events,
        syncTime: this.lastSyncTime
      }
    });
    window.dispatchEvent(event);
  }

  /**
   * Force a manual sync
   */
  async forcSync() {
    if (!this.isReady()) {
      throw new Error('Google Calendar service not ready');
    }

    return await this.syncCalendarEvents();
  }

  /**
   * Verify authentication state and enable calendar integration
   * This method ensures the auth state is fresh before enabling
   */
  async enableWithAuthVerification() {
    console.log('Enabling Google Calendar with authentication verification...');

    if (!this.authService) {
      throw new Error('Authentication service not available');
    }

    // Wait for auth service initialization
    if (!this.authService.isInitialized) {
      console.log('Waiting for auth service initialization...');
      await this.authService.waitForInitialization();
    }

    // Use comprehensive authentication verification if available
    let authVerification;
    if (this.authService.verifyAuthenticationComprehensively) {
      console.log('Performing comprehensive authentication verification...');
      authVerification = await this.authService.verifyAuthenticationComprehensively();

      if (!authVerification.isValid) {
        const errorMsg = `Authentication verification failed - ${authVerification.reason}. Please sign in first.`;
        console.error(errorMsg);
        throw new Error(errorMsg);
      }
    } else {
      // Fallback to legacy validation
      console.log('Using legacy authentication validation...');
      let authStatus;
      if (this.authService.validateAndFixAuthState) {
        console.log('Validating and fixing auth state before enabling calendar...');
        authStatus = await this.authService.validateAndFixAuthState();
      } else {
        console.log('Getting current auth status...');
        authStatus = this.authService.getAuthStatus();
      }

      console.log('Auth status before calendar enable:', {
        isAuthenticated: authStatus.isAuthenticated,
        hasAccessToken: authStatus.hasAccessToken,
        isTokenExpired: authStatus.isTokenExpired,
        isInitialized: authStatus.isInitialized,
        hasUserInfo: !!authStatus.userInfo
      });

      // Verify we have valid authentication with detailed error messages
      if (!authStatus.isAuthenticated) {
        const errorMsg = 'Authentication verification failed - user is not authenticated. Please sign in first.';
        console.error(errorMsg);
        throw new Error(errorMsg);
      }

      if (!authStatus.hasAccessToken) {
        const errorMsg = 'Authentication verification failed - no access token available. Please sign in again.';
        console.error(errorMsg);
        throw new Error(errorMsg);
      }

      if (authStatus.isTokenExpired) {
        const errorMsg = 'Authentication verification failed - access token has expired. Please sign in again.';
        console.error(errorMsg);
        throw new Error(errorMsg);
      }
    }

    console.log('Authentication verification passed, proceeding with enable...');

    // Now call the regular enable method
    return await this.enable();
  }

  /**
   * Enable calendar integration with retry logic for authentication issues
   */
  async enableWithRetry(maxRetries = 2, delayMs = 1000) {
    console.log(`Attempting to enable calendar integration with retry logic (max retries: ${maxRetries})...`);

    for (let attempt = 1; attempt <= maxRetries + 1; attempt++) {
      try {
        console.log(`Calendar enable attempt ${attempt}/${maxRetries + 1}`);

        if (attempt > 1) {
          console.log(`Waiting ${delayMs}ms before retry...`);
          await new Promise(resolve => setTimeout(resolve, delayMs));
        }

        await this.enableWithAuthVerification();
        console.log('Calendar integration enabled successfully');
        return true;
      } catch (error) {
        console.error(`Calendar enable attempt ${attempt} failed:`, error.message);

        if (attempt <= maxRetries) {
          console.log(`Will retry in ${delayMs}ms...`);
          // Exponential backoff for subsequent retries
          delayMs *= 1.5;
        } else {
          console.error('All calendar enable attempts failed');
          throw error;
        }
      }
    }
  }
}

// Export for use in other modules
window.GoogleCalendarService = GoogleCalendarService;
