<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Debug Tool</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status-section {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #007cba;
            background-color: #f8f9fa;
        }
        .error { border-left-color: #dc3545; background-color: #f8d7da; }
        .success { border-left-color: #28a745; background-color: #d4edda; }
        .warning { border-left-color: #ffc107; background-color: #fff3cd; }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #005a8b; }
        button:disabled { background: #ccc; cursor: not-allowed; }
        .log-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .auth-details {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Authentication Debug Tool</h1>
        <p>This tool helps debug Google authentication and calendar integration issues.</p>

        <div class="status-section">
            <h3>Authentication Status</h3>
            <div id="auth-status">Loading...</div>
            <div id="auth-details" class="auth-details"></div>
        </div>

        <div class="status-section">
            <h3>Calendar Integration Status</h3>
            <div id="calendar-status">Loading...</div>
        </div>

        <div class="status-section">
            <h3>Actions</h3>
            <button id="check-auth">Check Authentication</button>
            <button id="verify-comprehensive">Comprehensive Verification</button>
            <button id="validate-fix">Validate & Fix State</button>
            <button id="post-signin-validate">Post-SignIn Validation</button>
            <button id="test-calendar">Test Calendar Integration</button>
            <button id="clear-auth">Clear Authentication</button>
            <button id="clear-logs">Clear Logs</button>
        </div>

        <div class="status-section">
            <h3>Debug Logs</h3>
            <div id="log-area" class="log-area"></div>
        </div>
    </div>

    <!-- Include the authentication services -->
    <script src="google-auth.js"></script>
    <script src="google-calendar-service.js"></script>

    <script>
        let authService;
        let calendarService;
        let logArea;

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            logArea.textContent += logEntry;
            logArea.scrollTop = logArea.scrollHeight;

            console.log(message);
        }

        function updateAuthStatus(status) {
            const authStatusEl = document.getElementById('auth-status');
            const authDetailsEl = document.getElementById('auth-details');

            if (status.isAuthenticated && status.hasAccessToken && !status.isTokenExpired) {
                authStatusEl.innerHTML = '<span style="color: green;">✓ Authenticated</span>';
                authStatusEl.parentElement.className = 'status-section success';
            } else {
                authStatusEl.innerHTML = '<span style="color: red;">✗ Not Authenticated</span>';
                authStatusEl.parentElement.className = 'status-section error';
            }

            authDetailsEl.textContent = JSON.stringify({
                isAuthenticated: status.isAuthenticated,
                hasAccessToken: status.hasAccessToken,
                isTokenExpired: status.isTokenExpired,
                isInitialized: status.isInitialized,
                hasUserInfo: !!status.userInfo,
                tokenExpiry: status.tokenExpiry ? new Date(status.tokenExpiry).toLocaleString() : null
            }, null, 2);
        }

        function updateCalendarStatus(isEnabled, error = null) {
            const calendarStatusEl = document.getElementById('calendar-status');

            if (error) {
                calendarStatusEl.innerHTML = `<span style="color: red;">✗ Error: ${error}</span>`;
                calendarStatusEl.parentElement.className = 'status-section error';
            } else if (isEnabled) {
                calendarStatusEl.innerHTML = '<span style="color: green;">✓ Calendar Integration Enabled</span>';
                calendarStatusEl.parentElement.className = 'status-section success';
            } else {
                calendarStatusEl.innerHTML = '<span style="color: orange;">○ Calendar Integration Disabled</span>';
                calendarStatusEl.parentElement.className = 'status-section warning';
            }
        }

        async function checkAuthentication() {
            log('Checking authentication status...');
            try {
                const status = await authService.getAuthStatusAsync();
                updateAuthStatus(status);
                log(`Authentication check complete: ${status.isAuthenticated ? 'Authenticated' : 'Not authenticated'}`);
                return status;
            } catch (error) {
                log(`Error checking authentication: ${error.message}`, 'error');
                throw error;
            }
        }

        async function verifyComprehensive() {
            log('Performing comprehensive authentication verification...');
            try {
                const result = await authService.verifyAuthenticationComprehensively();
                log(`Comprehensive verification result: ${result.isValid ? 'VALID' : 'INVALID'} - ${result.reason}`);

                if (result.isValid) {
                    await checkAuthentication();
                }

                return result;
            } catch (error) {
                log(`Error during comprehensive verification: ${error.message}`, 'error');
                throw error;
            }
        }

        async function validateAndFix() {
            log('Validating and fixing authentication state...');
            try {
                const status = await authService.validateAndFixAuthState();
                updateAuthStatus(status);
                log('Authentication state validation and fix complete');
                return status;
            } catch (error) {
                log(`Error during validation and fix: ${error.message}`, 'error');
                throw error;
            }
        }

        async function postSignInValidate() {
            log('Performing post-sign-in validation...');
            try {
                if (authService.validatePostSignInState) {
                    const result = await authService.validatePostSignInState();
                    log(`Post-sign-in validation result: ${result.isValid ? 'VALID' : 'INVALID'} - ${result.reason}`);

                    if (result.isValid) {
                        await checkAuthentication();
                    }

                    return result;
                } else {
                    log('Post-sign-in validation method not available');
                    return { isValid: false, reason: 'Method not available' };
                }
            } catch (error) {
                log(`Error during post-sign-in validation: ${error.message}`, 'error');
                throw error;
            }
        }

        async function testCalendarIntegration() {
            log('Testing calendar integration...');
            try {
                await calendarService.enableWithAuthVerification();
                updateCalendarStatus(true);
                log('Calendar integration test PASSED');

                // Disable it again for repeated testing
                await calendarService.disable();
                updateCalendarStatus(false);
                log('Calendar integration disabled for repeated testing');
            } catch (error) {
                log(`Calendar integration test FAILED: ${error.message}`, 'error');
                updateCalendarStatus(false, error.message);
                throw error;
            }
        }

        async function clearAuthentication() {
            log('Clearing authentication state...');
            try {
                await authService.clearAuthState();
                await checkAuthentication();
                updateCalendarStatus(false);
                log('Authentication state cleared');
            } catch (error) {
                log(`Error clearing authentication: ${error.message}`, 'error');
                throw error;
            }
        }

        function clearLogs() {
            logArea.textContent = '';
            log('Debug logs cleared');
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', async () => {
            logArea = document.getElementById('log-area');
            log('Authentication Debug Tool initialized');

            try {
                // Initialize services
                authService = new GoogleAuthService();
                calendarService = new GoogleCalendarService(authService);

                log('Services initialized, checking initial state...');
                await checkAuthentication();

                // Set up event listeners
                document.getElementById('check-auth').addEventListener('click', checkAuthentication);
                document.getElementById('verify-comprehensive').addEventListener('click', verifyComprehensive);
                document.getElementById('validate-fix').addEventListener('click', validateAndFix);
                document.getElementById('post-signin-validate').addEventListener('click', postSignInValidate);
                document.getElementById('test-calendar').addEventListener('click', testCalendarIntegration);
                document.getElementById('clear-auth').addEventListener('click', clearAuthentication);
                document.getElementById('clear-logs').addEventListener('click', clearLogs);

                log('Debug tool ready for use');
            } catch (error) {
                log(`Error initializing debug tool: ${error.message}`, 'error');
            }
        });
    </script>
</body>
</html>
